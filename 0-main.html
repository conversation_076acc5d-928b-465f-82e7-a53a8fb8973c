<!DOCTYPE html>
<html lang="ko">
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta http-equiv="Content-Script-Type" content="text/javascript">
	<meta http-equiv="Content-Style-Type" content="text/css">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta property="og:type" content="website">
	<meta property="og:url" content="">
	<meta property="og:title" content="">
	<meta property="og:image" content="assets/images/img.png">
	<meta property="og:image:width" content="1920">
	<meta property="og:image:height" content="1080">
	<meta property="og:description" content="AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.">
	<meta property="og:site_name" content="지큐브">
	<meta property="og:locale" content="en_US">
	<title>지큐브</title> 
	<link rel="shortcut icon" href="assets/favi/favicon.ico">
	<meta name="theme-color" content="#ffffff">
	<link href="https://cdn.jsdelivr.net/gh/sun-typeface/SUIT@2/fonts/static/woff2/SUIT.css" rel="stylesheet">
	<link rel="stylesheet" type="text/css" href="assets/css/jquery.fullpage.min.css" />
	<link rel="stylesheet" type="text/css" href="assets/css/swiper.jquery.min.css" />
	<link rel="stylesheet" type="text/css" href="assets/css/common.css" />
	<script type="text/javascript" src="assets/js/jquery-3.6.0.min.js"></script>
	<script type="text/javascript" src="assets/js/swiper-bundle.min.js"></script>
	<script type="text/javascript" src="assets/js/common.js"></script>
	<script type="text/javascript" src="assets/js/scrolloverflow.js"></script>
	<script type="text/javascript" src="assets/js/jquery.fullpage.min.js"></script>
	<script type="text/javascript" src="assets/js/jquery.fullpage.extensions.min.js"></script>
	<script type="text/javascript" src="assets/js/swiper-bundle.min.js"></script>
</head>
<body>
	<div class="wrap  main--wrap">
		<!-- 공통헤더 -->
		<div class="header">
			<div class="inner">
				<h1><a href="/"><span  class="blind">gcube</span></a></h1>
				<div class="gnb">
					<a href="javascript:;" class="btn__nav"><span class="blind">모바일 GNB 열기</span></a>
					<div class="nav">
						<div class="nav-top">
							<div class="logo"><a href="/"><span  class="blind">gcube</span></a></div>
							<a href="javascript:;" class="btn__gpu">GPU 관리</a>
						</div>
						<ul>
							<li><a href="1-price.html" target="_self">Price</a></li>
							<li><a href="2-docs.html" target="_self">Docs</a></li>
							<li><a href="3-faq.html" target="_self">FAQ</a></li>
							<li><a href="4-contact.html" target="_self">Contact</a></li>
						</ul>
						<a href="javascript:;" class="btn__gpu">GPU 관리</a>
					</div>
					<a href="javascript:;" class="btn__gpu">GPU 관리</a>
				</div>
			</div>
		</div>
		<!-- //공통헤더 -->

		<!-- 메인 컨텐츠 -->
		<div class="main__container">
			<div class="visual">
				<div class="swiper mainSwiper">
					<div class="swiper-wrapper">
						<div class="swiper-slide mv1">
							<div class="main-slide__content">
								<p>간편하게 공유하고 경제적으로 빌려쓰는</p>
								<h2>전 세계 GPU NETWORK</h2>
								<div>고정 비용이 아닌, 자원 사용량에 따라 비용을 부과하여 경제적으로<br />GPU를 사용할 수 있습니다.</div>
							</div>
						</div>
						<div class="swiper-slide mv2">
							<div class="main-slide__content">
								<p>사용한 만큼만 지불하는 합리적 비용</p>
								<h2>최대 70% 경제적인<br />GPU Cloud 플랫폼</h2>
								<div>고정 비용이 아닌, 자원 사용량에 따라 비용을 부과하여<br />경제적으로 GPU를 사용할 수 있습니다.</div>
							</div>
						</div>
					</div>
					<div class="swiper-pagination"></div>
				</div>
				<script>
					var mainSwiper = new Swiper(".mainSwiper", {
						speed: 1000,
						autoplay: {
						  delay: 6000,
						  disableOnInteraction: false,
						},
						loop: true,
						pagination: {
						  el: ".mainSwiper .swiper-pagination",
						  clickable: true,
						},
					});
				</script>
			</div>
			<div class="main__content main__content1">
				<div class="inner">
					<h2><span>최소 비용으로 효율적인 GPU 사용</span></h2>
					<div class="info--wrap benefit">
						<h3>
							 <div>성능은 좋지만 높은 비용, <span>비용은 낮지만 부족한 성능.</span></div>
							 <div>GPU 공유 서비스의 <span>한계를 극복했습니다.</span></div>
						</h3>
						<div class="swiper benefitSwiper">
							<ul class="swiper-wrapper benefit__list">
								<li class="swiper-slide">
									<dl>
										<dt><strong>Global GPU Grid</strong></dt>
										<dd>gcube는 Global GPU네트워킹을 통해 클라우드 컴퓨팅 성능을 강력하게 유지하면서, 경제적인 가격에 공급하는 GPU 공유경제 서비스입니다.</dd>
									</dl>
								</li>
								<li class="swiper-slide">
									<dl>
										<dt>어떤 환경에도 적용가능한<br /><strong>맞춤형 공급</strong></dt>
										<dd>클라우드 네이티브 기술을 이용하여 CSP의 GPU와 PC의 GPU를 결합하여 동시접속이 많은 서비스, 수요가 일정하지 않은 경우 등 다양한 맞춤형 서비스를 제공합니다.</dd>
									</dl>
								</li>
								<li class="swiper-slide">
									<dl>
										<dt>다수의 국내 GPU 자원<br />확보로 <strong>높은 안정성</strong></dt>
										<dd>국내 기반 서비스를 시작하며 다수의 공급자를 확보하여 다양한 GPU 자원, 빠른 네트워크를 통한 안정적인 서비스를 제공합니다. </dd>
									</dl>
								</li>
								<li class="swiper-slide">
									<dl>
										<dt><strong>누구나 저렴하게</strong><br />이용 할 수 있는 GPU</dt>
										<dd>공급자와의 직접 계약, 누구나 손쉽게 공급할 수 있는 기술로 공급 비용을 절감하여 보다 경제적으로 이용할 수 있습니다. </dd>
									</dl>
								</li>
							</ul>
							<div class="swiper-pagination"></div>
						</div>
						<script>
							let benefitSwiper = null;
							let activeTimer = null;
							let activeIndex = 0;
							function addActiveAnimation() {
							  const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
							  if (slides.length === 0) return;
							  slides.forEach(el => el.classList.remove('active'));
							  slides[0].classList.add('active');
							  activeIndex = 0;
							  activeTimer = setInterval(() => {
								slides[activeIndex].classList.remove('active');
								activeIndex = (activeIndex + 1) % slides.length;
								slides[activeIndex].classList.add('active');
							  }, 4000);
							}
							function removeActiveAnimation() {
							  clearInterval(activeTimer);
							  const slides = document.querySelectorAll('.benefitSwiper .swiper-slide');
							  slides.forEach(el => el.classList.remove('active'));
							  activeIndex = 0;
							}
							//swiper동작
							function enableSwiper() {
							  if (!benefitSwiper) {
								benefitSwiper = new Swiper(".benefitSwiper", {
								  pagination: {
									el: ".benefitSwiper .swiper-pagination",
									clickable: true,
								  },
								  autoplay: {
									delay: 4000,
									disableOnInteraction: false,
								  },
								});
							  }
							}
							//swiper해제
							function disableSwiper() {
							  if (benefitSwiper) {
								benefitSwiper.destroy(true, true);
								benefitSwiper = null;
							  }
							}
							function handleResponsiveSwiper() {
							  if (window.innerWidth <= 767) {
								enableSwiper();
							  } else {
								disableSwiper();
								removeActiveAnimation();
								addActiveAnimation();
							  }
							}
							handleResponsiveSwiper();
							window.addEventListener('resize', handleResponsiveSwiper);
							window.addEventListener('beforeunload', removeActiveAnimation);
						</script>
					</div>
					<div class="info--wrap efficiency">
						<h3>
							 <div>최대 70% 경제적으로 <span>사용하는 GPU.</span></div>
							 <div>비용 걱정 없이 사용한 만큼만 <span>이용하세요.</span></div>
						</h3>
						<div class="efficiency--wrap">
							<div class="graph--wrap">
								<h4>시간당 GPU 사용 비용</h4>
								<ul class="graph__list other">
									<li>
										<dl>
											<dd class="other"><div class="bar"><div>39,746원</div></div></dd>
											<dd class="mine"><div class="bar"><div>4,830원</div></div></dd>
											<dt>V100</dt>
										</dl>
									</li>
									<li>
										<dl>
											<dd class="other"><div class="bar"><div>137,646원</div></div></dd>
											<dd class="mine"><div class="bar"><div>34,720원</div></div></dd>
											<dt>T4</dt>
										</dl>
									</li>
									<li>
										<dl>
											<dd class="other"><div class="bar"><div>57,344원</div></div></dd>
											<dd class="mine"><div class="bar"><div>20,790원</div></div></dd>
											<dt>RTX 4090</dt>
										</dl>
									</li>
									<li>
										<dl>
											<dd class="other"><div class="bar"><div>34,272원</div></div></dd>
											<dd class="mine"><div class="bar"><div>1,610원</div></div></dd>
											<dt>RTX 4080</dt>
										</dl>
									</li>
								</ul>
							</div>
							<div class="comparison--wrap">
								<button class="comparison__title"><span>GPU를 선택하세요</span></button>
								<ul class="comparison__list">
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
									<li>RTX 4090 사용 시</li>
								</ul>
								<dl>
									<dt>1개월 기준</dt>
									<dd>
										<strong><span class="price">??</span>만원 부터</strong> 이용할 수 있습니다. 
									</dd>
								</dl>
								<p class="comparison__info">gcube는 GPU 이용 시 사용하지 않은 시간은 비용을 부과하지 않습니다. 실시간 모니터링을 통해 실제 리소스 사용 비율에 따라 비용이 부과됩니다.</p>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="main__content main__content2">
				<div class="inner">
					<h2><span>경제적이고 다양한 GPU</span></h2>
					<div class="info--wrap variety">
						<h3 class="pc-only">
							 <div>gcube에서 원하는 목적과 스펙의</div>
							 <div>다양한 GPU를 선택할 수 있습니다.</div>
						</h3>
						<h3 class="mo-only">
							 <div>gcube에서 원하는</div>
							 <div>목적과 스펙의 다양한 GPU를</div>
							 <div>선택할 수 있습니다.</div>
						</h3>
					</div>
				</div>
			</div>
			<div class="main__content main__content3"><h2>섹션3</h2><h3>섹션3</h3></div>
		</div>

		<!-- 공통dim -->
		<div class="dim"></div>
	</div>
	<script>
		const contents = Array.from(document.querySelectorAll('[class^=main__content]'));
		const visual = document.querySelector('.visual');
		const mainContainer = document.querySelector('.main__container');
		let vh, heights, stickPoints;
		let lastFixedIndex = -1;
		function calculateMetrics() {
		  console.log('calculateMetrics')
		  vh = window.innerHeight;
		  heights = contents.map(c => c.clientHeight);
		  stickPoints = [];
		  let accum = vh;
		  for (let i = 0; i < contents.length; i++) {
			stickPoints.push(accum);
			accum += heights[i];
		  }
		}
		function onScroll() {
		  console.log('onScroll')
		  const scrollY = window.scrollY;
		  const maxFixIndex = contents.length - 2;
		  let paddingTopTarget = vh;
		  if (scrollY + vh >= stickPoints[0] + heights[0]) {
			paddingTopTarget += heights[0];
			if (scrollY + vh >= stickPoints[1] + heights[1]) {
			  paddingTopTarget += heights[1];
			}
		  }
		  mainContainer.style.paddingTop = `${paddingTopTarget}px`;
		  for (let i = 0; i <= maxFixIndex; i++) {
			if (scrollY + vh >= stickPoints[i] + heights[i]) {
			  if (lastFixedIndex <= i) {
				contents[i].classList.add('content-fixed');
				lastFixedIndex = i;
			  }
			} else if (scrollY + vh < stickPoints[i] + heights[i] && i <= lastFixedIndex) {
			  contents[i].classList.remove('content-fixed');
			  lastFixedIndex = i - 1;
			}
		  }
		}
		calculateMetrics();
		window.addEventListener('scroll', onScroll);
		window.addEventListener('resize', () => {
		  calculateMetrics();
		  onScroll();
		});
	</script>
</body>
</html>