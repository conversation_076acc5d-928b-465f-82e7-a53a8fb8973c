document.addEventListener("DOMContentLoaded", function () {

  // GNB
  var btnNav = document.querySelector(".btn__nav");
  if (btnNav) {
    btnNav.addEventListener("click", function () {
	  document.documentElement.classList.toggle("nav-opened");
	  btnNav.classList.toggle("is-active");
    });
  }

  // 모달열기
  function modalOpen(obj) {
    var pops = obj.getAttribute("data-link");
    var popup = document.querySelector(pops);
    if (popup) {
      popup.style.display = "block";
      popup.style.opacity = 0;
      setTimeout(function () {
        popup.style.opacity = 1;
      }, 0);
      document.documentElement.classList.add("is-opened");
    }
  }

  // 모달닫기
  function modalClose(obj) {
    var popup = obj.closest(".modal__container");
    if (popup) {
      setTimeout(function () {
        popup.style.display = "none";
        popup.style.opacity = "";
      }, 500);
      popup.style.opacity = 0;
      document.documentElement.classList.remove("is-opened");
    }
  }

  // 모달열기 버튼
  document.querySelectorAll(".btn__modal").forEach(function (btn) {
    btn.addEventListener("click", function (e) {
      e.preventDefault();
      openpop(btn);
    });
  });

  // 모달닫기 버튼
  document.querySelectorAll(".modal__close").forEach(function (btn) {
    btn.addEventListener("click", function (e) {
      e.preventDefault();
      closepop(btn);
    });
  });

  // 토글 버튼
  document.querySelectorAll(".btn__tooggle").forEach(function (btn) {
    btn.addEventListener("click", function () {
      btn.classList.toggle("on");
    });
  });
});